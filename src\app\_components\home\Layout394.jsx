"use client";

import {  } from "@relume_io/relume-ui";
import React from "react";

export function Layout394() {
  return (
    <section id="relume" className="px-[5%] py-16 md:py-24 lg:py-28">
      <div className="container">
        <div className="mx-auto mb-12 w-full max-w-lg text-center md:mb-18 lg:mb-20">
          <h1 className="mb-5 text-5xl font-bold md:mb-6 md:text-7xl lg:text-8xl">
            Ons ecosysteem
          </h1>
        </div>
        <div className="grid auto-cols-fr grid-cols-1 gap-6 md:gap-8 lg:grid-cols-3">
          <div className="flex flex-col border border-border rounded-lg">
            <div className="flex flex-1 flex-col justify-center p-6 md:p-8">
              <div>
                <img
                  src="/brand/forestforward/full_logo_cut.png"
                  alt="Forest Forward Logo"
                  className="h-12 md:h-12 lg:h-14 w-auto object-contain"
                />
                <h2 className="mb-3 text-2xl font-bold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
                  DOE
                </h2>
                <p>
                  Bedrijfsbossen &#x2022; Schoolbossen &#x2022; Voedselbossen &#x2022;
                  Natuuropwaardering &#x2022; Dakboerderij
                </p>
              </div>
            </div>
            <div className="flex w-full flex-col items-center justify-center self-start rounded-b-lg">
              <img
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
                alt="Relume placeholder image 1"
                className="rounded-b-lg"
              />
            </div>
          </div>
          <div className="flex flex-col border border-border rounded-lg">
            <div className="flex flex-1 flex-col justify-center p-6 md:p-8">
              <div>
                <img
                  src="/brand/storyforward/full_logo_cut.png"
                  alt="Story Forward Logo"
                  className="h-12 md:h-12 lg:h-14 w-auto object-contain"
                />
                <h2 className="mb-3 text-2xl font-bold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
                  INSPIREER
                </h2>
                <p>
                  Storytelling &#x2022; Mediarelaties &#x2022; Strategische &#x2022; communicatie &#x2022;
                  Trainingen &#x2022; Branding &#x2022; webdevelopment
                </p>
              </div>
            </div>
            <div className="flex w-full flex-col items-center justify-center self-start rounded-b-lg">
              <img
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
                alt="Relume placeholder image 1"
                className="rounded-b-lg"  
              />
            </div>
          </div>
          <div className="flex flex-col border border-border rounded-lg">
            <div className="flex flex-1 flex-col justify-center p-6 md:p-8">
              <div>
                <img
                  src="/brand/lagom/full_logo_cut.png"
                  alt="Lagom Logo"
                  className="h-12 md:h-12 lg:h-14 w-auto object-contain"
                />                
                <h2 className="mb-3 text-2xl font-bold md:mb-4 md:text-3xl md:leading-[1.3] lg:text-4xl">
                  VERBIND
                </h2>
                <p>
                  Impact events &#x2022; Familiedagen &#x2022; Workshops &#x2022; Teambuildings &#x2022;
                  Keynotes
                </p>
              </div>
            </div>
            <div className="flex w-full flex-col items-center justify-center self-start rounded-b-lg">
              <img
                src="https://d22po4pjz3o32e.cloudfront.net/placeholder-image.svg"
                alt="Relume placeholder image 1"
                className="rounded-b-lg"
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
